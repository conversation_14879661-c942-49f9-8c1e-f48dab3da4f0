import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Check } from 'lucide-react';
import { cn } from '../utils/cn';

interface WizardStep {
  id: string;
  title: string;
  component: React.ReactNode;
}

interface WizardProps {
  steps: WizardStep[];
  currentStep: number;
  onComplete?: () => void;
}

export default function Wizard({ steps, currentStep, onComplete }: WizardProps) {
  const { t } = useTranslation();

  // Auto-complete when reaching the last step
  useEffect(() => {
    if (currentStep >= steps.length - 1 && onComplete) {
      // Optional: auto-complete logic can be added here if needed
    }
  }, [currentStep, steps.length, onComplete]);

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={cn(
                  "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-500",
                  index < currentStep
                    ? "bg-accent-500 border-accent-500 text-white"
                    : index === currentStep
                    ? "bg-accent-100 border-accent-500 text-accent-700 animate-pulse"
                    : "bg-white border-primary-300 text-primary-500"
                )}
              >
                {index < currentStep ? (
                  <Check className="w-5 h-5" />
                ) : (
                  <span className="text-sm font-semibold">{index + 1}</span>
                )}
              </div>

              <div className="ml-3 hidden sm:block">
                <p className={cn(
                  "text-sm font-medium transition-colors duration-500",
                  index <= currentStep ? "text-primary-900" : "text-primary-500"
                )}>
                  {step.title}
                </p>
              </div>

              {index < steps.length - 1 && (
                <div className={cn(
                  "hidden sm:block w-16 h-0.5 mx-4 transition-all duration-500",
                  index < currentStep ? "bg-accent-500" : "bg-primary-200"
                )} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="min-h-[500px] mb-8">
        <div className="animate-fade-in">
          {steps[currentStep]?.component}
        </div>
      </div>

      {/* Progress Info */}
      <div className="flex justify-center items-center">
        <div className="flex items-center gap-2 text-sm text-primary-600 bg-primary-50 px-4 py-2 rounded-full">
          <span className="font-medium">{currentStep + 1}</span>
          <span>/</span>
          <span>{steps.length}</span>
          <span className="ml-2 text-primary-500">
            {currentStep === 0 && "Selecione seus arquivos"}
            {currentStep === 1 && "Escolha o formato"}
            {currentStep === 2 && "Converta suas imagens"}
          </span>
        </div>
      </div>
    </div>
  );
}


