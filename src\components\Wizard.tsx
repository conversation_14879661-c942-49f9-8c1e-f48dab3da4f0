import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { ChevronLeft, ChevronRight, Check } from 'lucide-react';
import { cn } from '../utils/cn';

interface WizardStep {
  id: string;
  title: string;
  component: React.ReactNode;
}

interface WizardProps {
  steps: WizardStep[];
  onComplete?: () => void;
  canProceed?: (stepIndex: number) => boolean;
}

export default function Wizard({ steps, onComplete, canProceed }: WizardProps) {
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(0);

  const goToNext = useCallback(() => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else if (onComplete) {
      onComplete();
    }
  }, [currentStep, steps.length, onComplete]);

  const goToPrevious = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const goToStep = useCallback((stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < steps.length) {
      setCurrentStep(stepIndex);
    }
  }, [steps.length]);

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <button
                onClick={() => goToStep(index)}
                className={cn(
                  "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300",
                  index < currentStep
                    ? "bg-accent-500 border-accent-500 text-white"
                    : index === currentStep
                    ? "bg-accent-100 border-accent-500 text-accent-700"
                    : "bg-white border-primary-300 text-primary-500 hover:border-accent-300"
                )}
              >
                {index < currentStep ? (
                  <Check className="w-5 h-5" />
                ) : (
                  <span className="text-sm font-semibold">{index + 1}</span>
                )}
              </button>
              
              <div className="ml-3 hidden sm:block">
                <p className={cn(
                  "text-sm font-medium transition-colors",
                  index <= currentStep ? "text-primary-900" : "text-primary-500"
                )}>
                  {step.title}
                </p>
              </div>
              
              {index < steps.length - 1 && (
                <div className={cn(
                  "hidden sm:block w-16 h-0.5 mx-4 transition-colors",
                  index < currentStep ? "bg-accent-500" : "bg-primary-200"
                )} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="min-h-[500px] mb-8">
        <div className="animate-fade-in">
          {steps[currentStep]?.component}
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center">
        <button
          onClick={goToPrevious}
          disabled={currentStep === 0}
          className={cn(
            "btn btn-outline flex items-center gap-2",
            currentStep === 0 && "opacity-50 cursor-not-allowed"
          )}
        >
          <ChevronLeft className="w-4 h-4" />
          {t('wizard.back')}
        </button>

        <div className="flex items-center gap-2 text-sm text-primary-600">
          <span>{currentStep + 1}</span>
          <span>/</span>
          <span>{steps.length}</span>
        </div>

        <button
          onClick={goToNext}
          disabled={canProceed && !canProceed(currentStep)}
          className="btn btn-primary flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {currentStep === steps.length - 1 ? t('wizard.finish') : t('wizard.next')}
          {currentStep < steps.length - 1 && <ChevronRight className="w-4 h-4" />}
        </button>
      </div>
    </div>
  );
}

// Hook para controlar o wizard externamente
export function useWizard(totalSteps: number) {
  const [currentStep, setCurrentStep] = useState(0);

  const next = useCallback(() => {
    setCurrentStep(prev => Math.min(prev + 1, totalSteps - 1));
  }, [totalSteps]);

  const previous = useCallback(() => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  }, []);

  const goTo = useCallback((step: number) => {
    setCurrentStep(Math.max(0, Math.min(step, totalSteps - 1)));
  }, [totalSteps]);

  const reset = useCallback(() => {
    setCurrentStep(0);
  }, []);

  return {
    currentStep,
    next,
    previous,
    goTo,
    reset,
    isFirst: currentStep === 0,
    isLast: currentStep === totalSteps - 1,
    progress: ((currentStep + 1) / totalSteps) * 100
  };
}
