import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Loader2, Download } from 'lucide-react';
import J<PERSON>Zip from 'jszip';
import FileUploader from './FileUploader';
import ConversionOptions from './ConversionOptions';
import FilePreview from './FilePreview';
import ConvertedFilePreview from './ConvertedFilePreview';
import { createPixelatedImage } from '../utils/imageProcessing';

export default function Converter() {
  const { t } = useTranslation();
  const [files, setFiles] = useState<File[]>([]);
  const [selectedFormat, setSelectedFormat] = useState<string>('png');
  const [isConverting, setIsConverting] = useState<boolean>(false);
  const [convertedFiles, setConvertedFiles] = useState<File[]>([]);
  
  const handleFilesAccepted = useCallback((acceptedFiles: File[]) => {
    setFiles((prevFiles) => [...prevFiles, ...acceptedFiles]);
  }, []);
  
  const handleRemoveFile = useCallback((index: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    setConvertedFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  }, []);
  
  const handleConvert = useCallback(async () => {
    if (!files.length) return;
    
    setIsConverting(true);
    
    try {
      const convertedBlobs = await Promise.all(
        files.map(file => createPixelatedImage(file, 8))
      );
      
      const convertedFiles = convertedBlobs.map((blob, index) => {
        const fileName = files[index].name.split('.')[0];
        return new File([blob], `${fileName}.${selectedFormat}`, {
          type: `image/${selectedFormat}`
        });
      });
      
      setConvertedFiles(convertedFiles);
    } catch (error) {
      console.error('Conversion error:', error);
    } finally {
      setIsConverting(false);
    }
  }, [files, selectedFormat]);
  
  const handleDownload = useCallback((index: number) => {
    if (!convertedFiles[index]) return;
    
    const file = convertedFiles[index];
    const url = URL.createObjectURL(file);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [convertedFiles]);

  const handleDownloadAll = useCallback(async () => {
    if (!convertedFiles.length) return;

    const zip = new JSZip();
    
    // Add all files to the zip
    convertedFiles.forEach((file) => {
      zip.file(file.name, file);
    });
    
    // Generate the zip file
    const content = await zip.generateAsync({ type: 'blob' });
    
    // Create download link
    const url = URL.createObjectURL(content);
    const link = document.createElement('a');
    link.href = url;
    link.download = `converted_images.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [convertedFiles]);
  
  return (
    <div className="space-y-8">
      <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-primary-900 mb-4 tracking-tight">
            {t('hero.title')}
          </h1>
          <p className="text-lg text-primary-600 max-w-2xl mx-auto leading-relaxed">
            {t('hero.subtitle')}
          </p>
        </div>
        <FileUploader onFilesAccepted={handleFilesAccepted} />
      </div>
      
      {files.length > 0 && (
        <div className="space-y-8 animate-fade-in">
          <ConversionOptions
            selectedFormat={selectedFormat}
            onFormatChange={setSelectedFormat}
          />

          <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8">
            <h3 className="text-2xl font-semibold text-primary-900 mb-6">
              Arquivos Selecionados
            </h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {files.map((file, index) => (
                <FilePreview 
                  key={`${file.name}-${index}`}
                  file={file} 
                  onRemove={() => handleRemoveFile(index)} 
                />
              ))}
            </div>
            
            <div className="mt-8 flex justify-center">
              <button
                className="btn btn-primary px-8 py-4 text-base font-semibold"
                onClick={handleConvert}
                disabled={isConverting || files.length === 0}
              >
                {isConverting ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-3 animate-spin" />
                    {t('converter.processing')}
                  </>
                ) : (
                  t('converter.convertButton')
                )}
              </button>
            </div>
          </div>
          
          {convertedFiles.length > 0 && (
            <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8 animate-fade-in">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-semibold text-primary-900">
                  Arquivos Convertidos
                </h3>
                {convertedFiles.length > 1 && (
                  <button
                    onClick={handleDownloadAll}
                    className="btn btn-outline flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Baixar Todos (.zip)
                  </button>
                )}
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {convertedFiles.map((file, index) => (
                  <ConvertedFilePreview 
                    key={`converted-${file.name}-${index}`}
                    originalFile={file}
                    convertedFormat={selectedFormat}
                    onDownload={() => handleDownload(index)}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}