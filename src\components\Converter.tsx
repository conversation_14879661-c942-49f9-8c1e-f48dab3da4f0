import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Loader2, Download } from 'lucide-react';
import J<PERSON>Zip from 'jszip';
import FileUploader from './FileUploader';
import ConversionOptions from './ConversionOptions';
import FilePreview from './FilePreview';
import ConvertedFilePreview from './ConvertedFilePreview';
import { createPixelatedImage } from '../utils/imageProcessing';

export default function Converter() {
  const { t } = useTranslation();
  const [files, setFiles] = useState<File[]>([]);
  const [selectedFormat, setSelectedFormat] = useState<string>('png');
  const [isConverting, setIsConverting] = useState<boolean>(false);
  const [convertedFiles, setConvertedFiles] = useState<File[]>([]);
  
  const handleFilesAccepted = useCallback((acceptedFiles: File[]) => {
    setFiles((prevFiles) => [...prevFiles, ...acceptedFiles]);
  }, []);
  
  const handleRemoveFile = useCallback((index: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    setConvertedFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  }, []);
  
  const handleConvert = useCallback(async () => {
    if (!files.length) return;
    
    setIsConverting(true);
    
    try {
      const convertedBlobs = await Promise.all(
        files.map(file => createPixelatedImage(file, 8))
      );
      
      const convertedFiles = convertedBlobs.map((blob, index) => {
        const fileName = files[index].name.split('.')[0];
        return new File([blob], `${fileName}.${selectedFormat}`, {
          type: `image/${selectedFormat}`
        });
      });
      
      setConvertedFiles(convertedFiles);
    } catch (error) {
      console.error('Conversion error:', error);
    } finally {
      setIsConverting(false);
    }
  }, [files, selectedFormat]);
  
  const handleDownload = useCallback((index: number) => {
    if (!convertedFiles[index]) return;
    
    const file = convertedFiles[index];
    const url = URL.createObjectURL(file);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [convertedFiles]);

  const handleDownloadAll = useCallback(async () => {
    if (!convertedFiles.length) return;

    const zip = new JSZip();
    
    // Add all files to the zip
    convertedFiles.forEach((file) => {
      zip.file(file.name, file);
    });
    
    // Generate the zip file
    const content = await zip.generateAsync({ type: 'blob' });
    
    // Create download link
    const url = URL.createObjectURL(content);
    const link = document.createElement('a');
    link.href = url;
    link.download = `converted_images.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [convertedFiles]);
  
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {t('hero.title')}
        </h1>
        <p className="text-gray-600 mb-6">
          {t('hero.subtitle')}
        </p>
        <FileUploader onFilesAccepted={handleFilesAccepted} />
      </div>
      
      {files.length > 0 && (
        <div className="space-y-6 animate-fade-in">
          <ConversionOptions 
            selectedFormat={selectedFormat}
            onFormatChange={setSelectedFormat}
          />
          
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              {t('converter.uploadTitle')}
            </h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {files.map((file, index) => (
                <FilePreview 
                  key={`${file.name}-${index}`}
                  file={file} 
                  onRemove={() => handleRemoveFile(index)} 
                />
              ))}
            </div>
            
            <div className="mt-6">
              <button 
                className="btn btn-primary px-6 py-2.5"
                onClick={handleConvert}
                disabled={isConverting || files.length === 0}
              >
                {isConverting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    {t('converter.processing')}
                  </>
                ) : (
                  t('converter.convertButton')
                )}
              </button>
            </div>
          </div>
          
          {convertedFiles.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm p-6 animate-fade-in">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {t('converter.downloadButton')}
                </h3>
                {convertedFiles.length > 1 && (
                  <button
                    onClick={handleDownloadAll}
                    className="btn btn-outline flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Baixar Todos (.zip)
                  </button>
                )}
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {convertedFiles.map((file, index) => (
                  <ConvertedFilePreview 
                    key={`converted-${file.name}-${index}`}
                    originalFile={file}
                    convertedFormat={selectedFormat}
                    onDownload={() => handleDownload(index)}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}