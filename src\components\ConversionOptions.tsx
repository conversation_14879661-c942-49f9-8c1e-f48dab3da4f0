import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircle } from 'lucide-react';
import { cn } from '../utils/cn';

interface FormatOption {
  id: string;
  icon: string;
}

interface ConversionOptionsProps {
  selectedFormat: string;
  onFormatChange: (format: string) => void;
}

export default function ConversionOptions({ 
  selectedFormat, 
  onFormatChange 
}: ConversionOptionsProps) {
  const { t } = useTranslation();
  
  const formats = useMemo<FormatOption[]>(() => [
    { id: 'ico', icon: '🖼️' },
    { id: 'png', icon: '🎨' },
    { id: 'jpg', icon: '📷' },
    { id: 'gif', icon: '🎞️' },
    { id: 'webp', icon: '🌐' },
    { id: 'svg', icon: '✨' },
  ], []);

  return (
    <div className="bg-white rounded-lg shadow-sm p-6 animate-slide-up">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        {t('converter.selectFormat')}
      </h3>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-3">
        {formats.map((format) => (
          <button
            key={format.id}
            className={cn(
              "relative flex flex-col items-center justify-center p-4 rounded-lg border-2 transition-all duration-200",
              selectedFormat === format.id 
                ? "border-primary-500 bg-primary-50"
                : "border-gray-200 bg-white hover:border-primary-200 hover:bg-primary-50/30"
            )}
            onClick={() => onFormatChange(format.id)}
          >
            <span className="text-2xl mb-2">{format.icon}</span>
            <span className="text-sm font-medium text-gray-700">
              {t(`formats.${format.id}`)}
            </span>
            
            {selectedFormat === format.id && (
              <div className="absolute -top-2 -right-2">
                <CheckCircle className="w-5 h-5 text-primary-500 fill-white" />
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
}