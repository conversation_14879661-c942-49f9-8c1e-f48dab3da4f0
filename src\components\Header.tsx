import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Menu, X, Globe } from 'lucide-react';
import { cn } from '../utils/cn';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { t, i18n } = useTranslation();
  
  const toggleLanguage = () => {
    const newLang = i18n.language === 'pt' ? 'en' : 'pt';
    i18n.changeLanguage(newLang);
  };
  
  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="container-custom">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center space-x-2">
            <svg 
              viewBox="0 0 24 24" 
              className="w-8 h-8 text-primary-500" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            >
              <path d="M21.9 8.5a2.8 2.8 0 0 0-2.03-2.65L7.13 2.1a2.8 2.8 0 0 0-3.53 1.65L2.1 7.13A2.8 2.8 0 0 0 3.75 10.7L5 11.27V19a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-7.73l1.24-.57A2.8 2.8 0 0 0 21.9 8.5z"/>
              <path d="M7 16h.01"/>
              <path d="M17 16h.01"/>
              <path d="M11.25 12h1.5L14 16h-4l1.25-4z"/>
            </svg>
            <span className="font-semibold text-xl text-gray-900">ConvertiCo</span>
          </div>
          
          <nav className="hidden md:flex space-x-8">
            <a href="#" className="text-gray-700 hover:text-primary-500 font-medium">{t('header.home')}</a>
            <a href="#" className="text-gray-700 hover:text-primary-500 font-medium">{t('header.about')}</a>
            <a href="#" className="text-gray-700 hover:text-primary-500 font-medium">{t('header.contact')}</a>
            <a href="#" className="text-gray-700 hover:text-primary-500 font-medium">{t('header.faq')}</a>
          </nav>
          
          <div className="flex items-center space-x-4">
            <button
              onClick={toggleLanguage}
              className="p-2 rounded-full hover:bg-gray-100"
              aria-label="Toggle language"
            >
              <Globe className="w-5 h-5 text-gray-700" />
            </button>
            
            <button 
              onClick={toggleMenu}
              className="md:hidden p-2 rounded-full hover:bg-gray-100"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
            </button>
          </div>
        </div>
      </div>
      
      {/* Mobile menu */}
      <div 
        className={cn(
          "md:hidden absolute w-full bg-white shadow-md transition-all duration-300 ease-in-out", 
          isMenuOpen ? "max-h-64 opacity-100" : "max-h-0 opacity-0 invisible"
        )}
      >
        <div className="container-custom py-4 space-y-4">
          <a href="#" className="block text-gray-700 hover:text-primary-500 font-medium">{t('header.home')}</a>
          <a href="#" className="block text-gray-700 hover:text-primary-500 font-medium">{t('header.about')}</a>
          <a href="#" className="block text-gray-700 hover:text-primary-500 font-medium">{t('header.contact')}</a>
          <a href="#" className="block text-gray-700 hover:text-primary-500 font-medium">{t('header.faq')}</a>
        </div>
      </div>
    </header>
  );
}