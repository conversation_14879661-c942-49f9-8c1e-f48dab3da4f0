@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', sans-serif;
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
  
  ::selection {
    @apply bg-primary-500/20;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600;
  }
  
  .btn-secondary {
    @apply bg-secondary-500 text-white hover:bg-secondary-600;
  }
  
  .btn-outline {
    @apply border border-gray-300 bg-transparent hover:bg-gray-100 text-gray-700;
  }
  
  .container-custom {
    @apply px-4 mx-auto max-w-7xl sm:px-6 lg:px-8;
  }
  
  .drop-area {
    @apply border-2 border-dashed rounded-lg p-8 transition-all duration-200 ease-in-out;
  }
  
  .drop-area-active {
    @apply border-primary-500 bg-primary-50;
  }
}