import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCir<PERSON>, Clock } from 'lucide-react';
import ConversionOptions from '../ConversionOptions';

interface FormatStepProps {
  selectedFormat: string;
  onFormatChange: (format: string) => void;
  filesCount: number;
}

export default function FormatStep({
  selectedFormat,
  onFormatChange,
  filesCount
}: FormatStepProps) {
  const { t } = useTranslation();
  const [showTransition, setShowTransition] = useState(false);
  const [previousFormat, setPreviousFormat] = useState(selectedFormat);

  // Show transition feedback when format is selected
  useEffect(() => {
    if (selectedFormat && selectedFormat !== previousFormat) {
      setShowTransition(true);
      setPreviousFormat(selectedFormat);
      const timer = setTimeout(() => {
        setShowTransition(false);
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [selectedFormat, previousFormat]);

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-primary-900 mb-4 tracking-tight">
          {t('wizard.step2')}
        </h2>
        <p className="text-lg text-primary-600 max-w-2xl mx-auto leading-relaxed">
          Escolha o formato para o qual deseja converter seus {filesCount} arquivo{filesCount > 1 ? 's' : ''}
        </p>
      </div>

      <ConversionOptions
        selectedFormat={selectedFormat}
        onFormatChange={onFormatChange}
      />

      {showTransition ? (
        <div className="text-center animate-fade-in">
          <div className="inline-flex items-center gap-3 bg-accent-50 text-accent-700 px-6 py-3 rounded-full">
            <Clock className="w-5 h-5 animate-spin" />
            <span className="font-medium">
              Avançando para conversão...
            </span>
          </div>
        </div>
      ) : (
        <div className="text-center">
          <div className="inline-flex items-center gap-2 text-primary-600">
            <CheckCircle className="w-5 h-5 text-accent-500" />
            <span>
              Formato selecionado: <span className="font-semibold text-primary-900">{selectedFormat.toUpperCase()}</span>
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
