import { useTranslation } from 'react-i18next';
import ConversionOptions from '../ConversionOptions';

interface FormatStepProps {
  selectedFormat: string;
  onFormatChange: (format: string) => void;
  filesCount: number;
}

export default function FormatStep({
  selectedFormat,
  onFormatChange,
  filesCount
}: FormatStepProps) {
  const { t } = useTranslation();

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-primary-900 mb-4 tracking-tight">
          {t('wizard.step2')}
        </h2>
        <p className="text-lg text-primary-600 max-w-2xl mx-auto leading-relaxed">
          Escolha o formato para o qual deseja converter seus {filesCount} arquivo{filesCount > 1 ? 's' : ''}
        </p>
      </div>

      <ConversionOptions
        selectedFormat={selectedFormat}
        onFormatChange={onFormatChange}
      />

      <div className="text-center">
        <p className="text-primary-600">
          Formato selecionado: <span className="font-semibold text-primary-900">{selectedFormat.toUpperCase()}</span>
        </p>
        <p className="text-sm text-primary-500 mt-2">
          Use o botão 'Próximo' para iniciar a conversão
        </p>
      </div>
    </div>
  );
}
