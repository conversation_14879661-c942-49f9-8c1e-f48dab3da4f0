import { useTranslation } from 'react-i18next';
import { Facebook, Twitter, Instagram } from 'lucide-react';

export default function Footer() {
  const { t } = useTranslation();
  
  return (
    <footer className="bg-gray-900 text-gray-200 mt-16">
      <div className="container-custom py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center space-x-2">
              <svg 
                viewBox="0 0 24 24" 
                className="w-8 h-8 text-primary-400" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              >
                <path d="M21.9 8.5a2.8 2.8 0 0 0-2.03-2.65L7.13 2.1a2.8 2.8 0 0 0-3.53 1.65L2.1 7.13A2.8 2.8 0 0 0 3.75 10.7L5 11.27V19a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-7.73l1.24-.57A2.8 2.8 0 0 0 21.9 8.5z"/>
                <path d="M7 16h.01"/>
                <path d="M17 16h.01"/>
                <path d="M11.25 12h1.5L14 16h-4l1.25-4z"/>
              </svg>
              <span className="font-semibold text-xl">ConvertiCo</span>
            </div>
            <p className="mt-4 text-gray-400">
              Ferramenta gratuita para converter imagens entre diferentes formatos, de maneira fácil e rápida.
            </p>
            <div className="flex space-x-4 mt-6">
              <a href="#" className="text-gray-400 hover:text-white transition">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition">
                <Instagram className="w-5 h-5" />
              </a>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-lg mb-4">Links Rápidos</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-400 hover:text-white transition">Início</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition">Sobre Nós</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition">Contato</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition">FAQ</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold text-lg mb-4">Formatos</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-400 hover:text-white transition">ICO</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition">PNG</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition">JPG</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition">GIF</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition">WebP</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition">SVG</a></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold text-lg mb-4">Legal</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-400 hover:text-white transition">{t('footer.terms')}</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition">{t('footer.privacy')}</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition">Cookies</a></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-12 pt-8">
          <p className="text-center text-gray-500 text-sm">{t('footer.copyright')}</p>
        </div>
      </div>
    </footer>
  );
}