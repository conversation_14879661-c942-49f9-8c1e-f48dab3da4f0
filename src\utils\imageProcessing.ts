export async function convertImage(
  originalImage: File,
  targetFormat: string,
  quality: number = 0.9
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(originalImage);

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;
        canvas.width = img.width;
        canvas.height = img.height;

        // Handle different background colors for different formats
        if (targetFormat === 'jpg' || targetFormat === 'jpeg' || targetFormat === 'bmp') {
          // Fill with white background for formats that don't support transparency
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        // Draw the image
        ctx.drawImage(img, 0, 0);

        // Handle special formats
        if (targetFormat === 'pdf') {
          // For PDF, we'll create a simple PDF with the image
          convertToPDF(canvas, originalImage.name).then(resolve).catch(reject);
          return;
        }

        // Get the appropriate MIME type
        const mimeType = getMimeType(targetFormat);

        // Convert to blob
        canvas.toBlob((blob) => {
          URL.revokeObjectURL(url);
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to convert image'));
          }
        }, mimeType, quality);

      } catch (error) {
        URL.revokeObjectURL(url);
        reject(error);
      }
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };

    img.src = url;
  });
}

function getMimeType(format: string): string {
  const mimeTypes: Record<string, string> = {
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'bmp': 'image/bmp',
    'tiff': 'image/tiff',
    'ico': 'image/x-icon',
    'svg': 'image/svg+xml'
  };

  return mimeTypes[format] || 'image/png';
}

async function convertToPDF(canvas: HTMLCanvasElement, originalName: string): Promise<Blob> {
  // Simple PDF creation - in a real app, you'd use a library like jsPDF
  // For now, we'll create a basic PDF structure
  const imgData = canvas.toDataURL('image/jpeg', 0.9);

  // This is a simplified PDF creation - in production, use jsPDF or similar
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/XObject <<
/Im1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
q
${canvas.width} 0 0 ${canvas.height} 50 400 cm
/Im1 Do
Q
endstream
endobj

5 0 obj
<<
/Type /XObject
/Subtype /Image
/Width ${canvas.width}
/Height ${canvas.height}
/ColorSpace /DeviceRGB
/BitsPerComponent 8
/Filter /DCTDecode
/Length ${imgData.length}
>>
stream
${imgData}
endstream
endobj

xref
0 6
0000000000 65535 f
0000000010 00000 n
0000000053 00000 n
0000000125 00000 n
0000000348 00000 n
0000000565 00000 n
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
${800 + imgData.length}
%%EOF`;

  return new Blob([pdfContent], { type: 'application/pdf' });
}

// Legacy function for backward compatibility
export async function createPixelatedImage(
  originalImage: File,
  scale: number = 8
): Promise<Blob> {
  return convertImage(originalImage, 'png', 0.9);
}