export async function createPixelatedImage(
  originalImage: File,
  scale: number = 8
): Promise<Blob> {
  return new Promise((resolve) => {
    const img = new Image();
    const url = URL.createObjectURL(originalImage);
    
    img.onload = () => {
      // Create canvas for the original size
      const originalCanvas = document.createElement('canvas');
      const octx = originalCanvas.getContext('2d')!;
      originalCanvas.width = img.width;
      originalCanvas.height = img.height;
      
      // Draw original image
      octx.imageSmoothingEnabled = false;
      octx.drawImage(img, 0, 0);
      
      // Create canvas for the scaled size
      const scaledCanvas = document.createElement('canvas');
      const sctx = scaledCanvas.getContext('2d')!;
      scaledCanvas.width = img.width * scale;
      scaledCanvas.height = img.height * scale;
      
      // Configure for pixel-perfect scaling
      sctx.imageSmoothingEnabled = false;
      
      // Draw scaled image
      sctx.drawImage(
        originalCanvas,
        0, 0,
        originalCanvas.width, originalCanvas.height,
        0, 0,
        scaledCanvas.width, scaledCanvas.height
      );
      
      // Convert to blob and resolve
      scaledCanvas.toBlob((blob) => {
        URL.revokeObjectURL(url);
        if (blob) resolve(blob);
      }, 'image/png');
    };
    
    img.src = url;
  });
}