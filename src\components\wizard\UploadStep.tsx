import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCir<PERSON>, Clock } from 'lucide-react';
import FileUploader from '../FileUploader';
import FilePreview from '../FilePreview';

interface UploadStepProps {
  files: File[];
  onFilesAccepted: (files: File[]) => void;
  onRemoveFile: (index: number) => void;
}

export default function UploadStep({
  files,
  onFilesAccepted,
  onRemoveFile
}: UploadStepProps) {
  const { t } = useTranslation();
  const [showTransition, setShowTransition] = useState(false);

  // Show transition feedback when files are selected
  useEffect(() => {
    if (files.length > 0) {
      setShowTransition(true);
      const timer = setTimeout(() => {
        setShowTransition(false);
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [files.length]);

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-primary-900 mb-4 tracking-tight">
            {t('wizard.step1')}
          </h2>
          <p className="text-lg text-primary-600 max-w-2xl mx-auto leading-relaxed">
            {t('hero.subtitle')}
          </p>
          {files.length === 0 && (
            <div className="mt-4 text-sm text-primary-500 bg-primary-50 px-4 py-2 rounded-xl inline-block">
              ✨ Navegação automática ativada - apenas selecione e converta!
            </div>
          )}
        </div>
        <FileUploader onFilesAccepted={onFilesAccepted} />
      </div>

      {files.length > 0 && (
        <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8 animate-fade-in">
          <h3 className="text-2xl font-semibold text-primary-900 mb-6">
            Arquivos Selecionados ({files.length})
          </h3>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {files.map((file, index) => (
              <FilePreview 
                key={`${file.name}-${index}`}
                file={file} 
                onRemove={() => onRemoveFile(index)} 
              />
            ))}
          </div>

          {showTransition ? (
            <div className="mt-8 text-center animate-fade-in">
              <div className="inline-flex items-center gap-3 bg-accent-50 text-accent-700 px-6 py-3 rounded-full">
                <Clock className="w-5 h-5 animate-spin" />
                <span className="font-medium">
                  Avançando para seleção de formato...
                </span>
              </div>
            </div>
          ) : (
            <div className="mt-6 text-center">
              <div className="inline-flex items-center gap-2 text-primary-600">
                <CheckCircle className="w-5 h-5 text-accent-500" />
                <span>
                  {files.length === 1
                    ? "1 arquivo selecionado"
                    : `${files.length} arquivos selecionados`
                  }
                </span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
