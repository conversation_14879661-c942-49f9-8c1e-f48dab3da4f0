import { useTranslation } from 'react-i18next';
import FileUploader from '../FileUploader';
import FilePreview from '../FilePreview';

interface UploadStepProps {
  files: File[];
  onFilesAccepted: (files: File[]) => void;
  onRemoveFile: (index: number) => void;
}

export default function UploadStep({
  files,
  onFilesAccepted,
  onRemoveFile
}: UploadStepProps) {
  const { t } = useTranslation();

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-primary-900 mb-4 tracking-tight">
            {t('wizard.step1')}
          </h2>
          <p className="text-lg text-primary-600 max-w-2xl mx-auto leading-relaxed">
            {t('hero.subtitle')}
          </p>
        </div>
        <FileUploader onFilesAccepted={onFilesAccepted} />
      </div>

      {files.length > 0 && (
        <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8 animate-fade-in">
          <h3 className="text-2xl font-semibold text-primary-900 mb-6">
            Arquivos Selecionados ({files.length})
          </h3>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {files.map((file, index) => (
              <FilePreview 
                key={`${file.name}-${index}`}
                file={file} 
                onRemove={() => onRemoveFile(index)} 
              />
            ))}
          </div>

          <div className="mt-6 text-center">
            <p className="text-primary-600">
              {files.length === 1
                ? "1 arquivo selecionado. Use o botão 'Próximo' para continuar."
                : `${files.length} arquivos selecionados. Use o botão 'Próximo' para continuar.`
              }
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
