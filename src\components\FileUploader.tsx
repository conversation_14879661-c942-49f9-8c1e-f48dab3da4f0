import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDropzone } from 'react-dropzone';
import { Upload, File } from 'lucide-react';
import { cn } from '../utils/cn';

interface FileUploaderProps {
  onFilesAccepted: (files: File[]) => void;
}

export default function FileUploader({ onFilesAccepted }: FileUploaderProps) {
  const { t } = useTranslation();
  const [isDragActive, setIsDragActive] = useState(false);
  
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles?.length) {
      onFilesAccepted(acceptedFiles);
    }
  }, [onFilesAccepted]);

  const { getRootProps, getInputProps, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.ico', '.webp', '.svg']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false)
  });

  return (
    <div 
      {...getRootProps()} 
      className={cn(
        "drop-area cursor-pointer bg-white focus:outline-none",
        isDragActive && "drop-area-active",
        isDragReject && "border-red-500 bg-red-50"
      )}
    >
      <input {...getInputProps()} />
      
      <div className="flex flex-col items-center justify-center text-center">
        <div className="rounded-full bg-primary-100 p-3 mb-4">
          <Upload className="w-8 h-8 text-primary-500" />
        </div>
        
        <h3 className="text-lg font-medium text-gray-900 mb-1">
          {isDragActive 
            ? t('converter.dropzoneActive') 
            : t('converter.dragHere')}
        </h3>
        
        <p className="text-sm text-gray-500">
          {t('converter.orClick')}
        </p>
        
        <p className="mt-4 text-xs text-gray-400">
          {t('converter.uploadSubtitle')}
        </p>
        
        {isDragReject && (
          <p className="mt-2 text-sm text-red-500">
            Arquivo não suportado. Por favor, selecione apenas arquivos de imagem.
          </p>
        )}
      </div>
    </div>
  );
}