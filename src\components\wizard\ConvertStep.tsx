import { useTranslation } from 'react-i18next';
import { Loader2, Download, CheckCircle, RefreshCw } from 'lucide-react';
import ConvertedFilePreview from '../ConvertedFilePreview';

interface ConvertStepProps {
  files: File[];
  convertedFiles: File[];
  selectedFormat: string;
  isConverting: boolean;
  onConvert: () => void;
  onDownload: (index: number) => void;
  onDownloadAll: () => void;
  onReset?: () => void;
}

export default function ConvertStep({ 
  files,
  convertedFiles,
  selectedFormat,
  isConverting,
  onConvert,
  onDownload,
  onDownloadAll,
  onReset
}: ConvertStepProps) {
  const { t } = useTranslation();

  const hasStartedConversion = convertedFiles.length > 0 || isConverting;
  const isComplete = convertedFiles.length === files.length && !isConverting;

  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-primary-900 mb-4 tracking-tight">
          {t('wizard.step3')}
        </h2>
        <p className="text-lg text-primary-600 max-w-2xl mx-auto leading-relaxed">
          {isComplete 
            ? `Conversão concluída! ${files.length} arquivo${files.length > 1 ? 's' : ''} convertido${files.length > 1 ? 's' : ''} para ${selectedFormat.toUpperCase()}`
            : `Convertendo ${files.length} arquivo${files.length > 1 ? 's' : ''} para ${selectedFormat.toUpperCase()}`
          }
        </p>
      </div>

      {!hasStartedConversion && (
        <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8 text-center">
          <div className="mb-6">
            <div className="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-accent-600" />
            </div>
            <h3 className="text-xl font-semibold text-primary-900 mb-2">
              Pronto para Converter
            </h3>
            <p className="text-primary-600">
              {files.length} arquivo{files.length > 1 ? 's' : ''} será{files.length > 1 ? 'm' : ''} convertido{files.length > 1 ? 's' : ''} para {selectedFormat.toUpperCase()}
            </p>
          </div>
          
          <button
            onClick={onConvert}
            className="btn btn-primary px-8 py-4 text-base font-semibold"
          >
            {t('converter.convertButton')}
          </button>
        </div>
      )}

      {isConverting && (
        <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8 text-center">
          <div className="mb-6">
            <Loader2 className="w-16 h-16 text-accent-500 animate-spin mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-primary-900 mb-2">
              {t('converter.processing')}
            </h3>
            <p className="text-primary-600">
              Convertendo seus arquivos... Por favor, aguarde.
            </p>
          </div>
          
          <div className="w-full bg-primary-100 rounded-full h-2">
            <div 
              className="bg-accent-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(convertedFiles.length / files.length) * 100}%` }}
            />
          </div>
          <p className="text-sm text-primary-500 mt-2">
            {convertedFiles.length} de {files.length} arquivos convertidos
          </p>
        </div>
      )}

      {convertedFiles.length > 0 && !isConverting && (
        <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8 animate-fade-in">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-2xl font-semibold text-primary-900">
              Arquivos Convertidos
            </h3>
            <div className="flex gap-3">
              {convertedFiles.length > 1 && (
                <button
                  onClick={onDownloadAll}
                  className="btn btn-outline flex items-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  Baixar Todos (.zip)
                </button>
              )}
              {onReset && (
                <button
                  onClick={onReset}
                  className="btn btn-secondary flex items-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Nova Conversão
                </button>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {convertedFiles.map((file, index) => (
              <ConvertedFilePreview 
                key={`converted-${file.name}-${index}`}
                originalFile={file}
                convertedFormat={selectedFormat}
                onDownload={() => onDownload(index)}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
